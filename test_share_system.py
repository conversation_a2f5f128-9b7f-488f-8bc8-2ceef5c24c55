#!/usr/bin/env python3
"""
测试分享系统功能
"""

import os
import sys
import json
from share_system import ShareSystem

def test_share_system():
    """测试分享系统的基本功能"""
    print("开始测试分享系统...")
    
    # 创建测试用的分享系统实例
    test_data_file = 'data/test_shares.json'
    share_system = ShareSystem(test_data_file)
    
    # 测试数据
    test_images = [
        {
            'image_url': 'https://example.com/image1.jpg',
            'prompt': '测试提示词1',
            'username': 'testuser',
            'timestamp': '2025-01-01T12:00:00Z',
            'model_name': '测试模型'
        },
        {
            'image_url': 'https://example.com/image2.jpg',
            'prompt': '测试提示词2',
            'username': 'testuser',
            'timestamp': '2025-01-01T12:30:00Z',
            'model_name': '测试模型'
        }
    ]
    
    print("1. 测试创建分享链接...")
    share_id, access_key = share_system.create_share(
        creator_username='testuser',
        image_records=test_images,
        expire_days=7,
        require_key=True,
        title='测试分享'
    )
    
    if share_id and access_key:
        print(f"✓ 分享链接创建成功")
        print(f"  分享ID: {share_id}")
        print(f"  访问秘钥: {access_key}")
    else:
        print("✗ 分享链接创建失败")
        return False
    
    print("\n2. 测试获取分享信息...")
    share_data = share_system.get_share(share_id)
    if share_data:
        print(f"✓ 分享信息获取成功")
        print(f"  标题: {share_data['title']}")
        print(f"  图片数量: {len(share_data['images'])}")
        print(f"  需要秘钥: {share_data['require_key']}")
    else:
        print("✗ 分享信息获取失败")
        return False
    
    print("\n3. 测试访问权限验证...")
    
    # 测试正确的秘钥
    success, message, data = share_system.verify_share_access(share_id, access_key)
    if success:
        print("✓ 正确秘钥验证成功")
    else:
        print(f"✗ 正确秘钥验证失败: {message}")
        return False
    
    # 测试错误的秘钥
    success, message, data = share_system.verify_share_access(share_id, "WRONGKEY")
    if not success and "秘钥错误" in message:
        print("✓ 错误秘钥验证正确拒绝")
    else:
        print(f"✗ 错误秘钥验证未正确拒绝: {message}")
        return False
    
    print("\n4. 测试记录访问...")
    original_view_count = share_data['view_count']
    share_system.record_view(share_id)
    updated_share = share_system.get_share(share_id)
    if updated_share['view_count'] == original_view_count + 1:
        print("✓ 访问记录功能正常")
    else:
        print("✗ 访问记录功能异常")
        return False
    
    print("\n5. 测试获取用户分享列表...")
    user_shares = share_system.get_user_shares('testuser')
    if len(user_shares) >= 1:
        print(f"✓ 用户分享列表获取成功，共 {len(user_shares)} 个分享")
    else:
        print("✗ 用户分享列表获取失败")
        return False
    
    print("\n6. 测试创建无秘钥分享...")
    share_id2, access_key2 = share_system.create_share(
        creator_username='testuser',
        image_records=test_images[:1],
        expire_days=3,
        require_key=False,
        title='公开分享'
    )
    
    if share_id2 and access_key2 is None:
        print("✓ 无秘钥分享创建成功")
        
        # 测试无秘钥访问
        success, message, data = share_system.verify_share_access(share_id2)
        if success:
            print("✓ 无秘钥访问验证成功")
        else:
            print(f"✗ 无秘钥访问验证失败: {message}")
            return False
    else:
        print("✗ 无秘钥分享创建失败")
        return False
    
    print("\n7. 测试删除分享...")
    success, message = share_system.delete_share(share_id2, 'testuser')
    if success:
        print("✓ 分享删除成功")
        
        # 验证删除后无法访问
        success, message, data = share_system.verify_share_access(share_id2)
        if not success and "不存在" in message:
            print("✓ 删除后访问正确被拒绝")
        else:
            print(f"✗ 删除后仍可访问: {message}")
            return False
    else:
        print(f"✗ 分享删除失败: {message}")
        return False
    
    print("\n8. 测试统计信息...")
    stats = share_system.get_statistics()
    print(f"✓ 统计信息获取成功:")
    print(f"  总创建分享数: {stats['total_shares_created']}")
    print(f"  总浏览次数: {stats['total_views']}")
    print(f"  活跃分享数: {stats['active_shares']}")
    
    # 清理测试文件
    if os.path.exists(test_data_file):
        os.remove(test_data_file)
        print(f"\n✓ 测试文件已清理: {test_data_file}")
    
    print("\n🎉 所有测试通过！分享系统功能正常。")
    return True

if __name__ == '__main__':
    try:
        success = test_share_system()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
