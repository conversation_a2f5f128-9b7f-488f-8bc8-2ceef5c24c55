#!/usr/bin/env python3
"""
测试固定分享设置功能
"""

import os
import sys
from share_system import ShareSystem

def test_fixed_share_settings():
    """测试固定的分享设置（7天过期，必须需要秘钥）"""
    print("测试固定分享设置...")
    
    # 创建测试用的分享系统实例
    test_data_file = 'data/test_fixed_shares.json'
    share_system = ShareSystem(test_data_file)
    
    # 测试数据
    test_images = [
        {
            'image_url': 'https://example.com/image1.jpg',
            'prompt': '测试提示词1',
            'username': 'testuser',
            'timestamp': '2025-01-01T12:00:00Z',
            'model_name': '测试模型'
        }
    ]
    
    print("1. 测试创建分享（固定7天过期，必须需要秘钥）...")
    share_id, access_key = share_system.create_share(
        creator_username='testuser',
        image_records=test_images,
        expire_days=7,  # 固定7天
        require_key=True,  # 固定需要秘钥
        title='固定设置测试分享'
    )
    
    if share_id and access_key:
        print(f"✓ 分享链接创建成功")
        print(f"  分享ID: {share_id}")
        print(f"  访问秘钥: {access_key}")
    else:
        print("✗ 分享链接创建失败")
        return False
    
    print("\n2. 验证分享设置...")
    share_data = share_system.get_share(share_id)
    if share_data:
        print(f"✓ 分享信息获取成功")
        print(f"  需要秘钥: {share_data['require_key']}")
        
        # 验证必须需要秘钥
        if share_data['require_key'] == True:
            print("✓ 确认分享必须需要秘钥")
        else:
            print("✗ 分享设置错误：应该必须需要秘钥")
            return False
            
        # 验证过期时间设置（检查是否大约是7天后）
        from datetime import datetime, timedelta
        expire_time = datetime.fromisoformat(share_data['expire_at'].replace('Z', '+00:00'))
        created_time = datetime.fromisoformat(share_data['created_at'].replace('Z', '+00:00'))
        expire_days = (expire_time - created_time).days
        
        if expire_days == 7:
            print(f"✓ 确认过期时间设置为7天")
        else:
            print(f"✗ 过期时间设置错误：实际为{expire_days}天，应该为7天")
            return False
    else:
        print("✗ 分享信息获取失败")
        return False
    
    print("\n3. 测试访问权限（必须需要秘钥）...")
    
    # 测试无秘钥访问（应该失败）
    success, message, data = share_system.verify_share_access(share_id)
    if not success and "需要访问秘钥" in message:
        print("✓ 无秘钥访问正确被拒绝")
    else:
        print(f"✗ 无秘钥访问未被正确拒绝: {message}")
        return False
    
    # 测试正确秘钥访问（应该成功）
    success, message, data = share_system.verify_share_access(share_id, access_key)
    if success:
        print("✓ 正确秘钥访问成功")
    else:
        print(f"✗ 正确秘钥访问失败: {message}")
        return False
    
    # 测试错误秘钥访问（应该失败）
    success, message, data = share_system.verify_share_access(share_id, "WRONGKEY")
    if not success and "秘钥错误" in message:
        print("✓ 错误秘钥访问正确被拒绝")
    else:
        print(f"✗ 错误秘钥访问未被正确拒绝: {message}")
        return False
    
    print("\n4. 测试尝试创建无秘钥分享（应该仍然需要秘钥）...")
    share_id2, access_key2 = share_system.create_share(
        creator_username='testuser',
        image_records=test_images,
        expire_days=7,
        require_key=False,  # 尝试设置为不需要秘钥
        title='尝试无秘钥分享'
    )
    
    if share_id2:
        share_data2 = share_system.get_share(share_id2)
        if share_data2['require_key'] == False:
            print("⚠️  注意：系统仍允许创建无秘钥分享")
            print("   如果要强制所有分享都需要秘钥，需要在ShareSystem中修改")
        else:
            print("✓ 系统强制所有分享都需要秘钥")
    
    # 清理测试文件
    if os.path.exists(test_data_file):
        os.remove(test_data_file)
        print(f"\n✓ 测试文件已清理: {test_data_file}")
    
    print("\n🎉 固定分享设置测试完成！")
    print("📋 总结：")
    print("   - 分享过期时间：固定7天")
    print("   - 访问控制：必须需要秘钥")
    print("   - 未登录用户必须输入正确秘钥才能查看")
    
    return True

if __name__ == '__main__':
    try:
        success = test_fixed_share_settings()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
