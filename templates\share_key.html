<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问分享内容 - MiaoMiao AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .key-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 2rem;
            max-width: 400px;
            width: 100%;
        }
        
        .key-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .error-message {
            display: none;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="key-card text-center">
        <i class="fas fa-key key-icon"></i>
        <h2 class="mb-3">需要访问秘钥</h2>
        <p class="text-muted mb-4">此分享内容需要访问秘钥才能查看，请输入正确的秘钥。</p>
        
        <form id="keyForm">
            <div class="mb-3">
                <input type="text" class="form-control text-center" id="accessKey" 
                       placeholder="请输入访问秘钥" maxlength="20" style="font-size: 1.2rem; letter-spacing: 2px;">
            </div>
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-unlock me-2"></i>访问分享内容
            </button>
        </form>
        
        <div class="alert alert-danger error-message" id="errorMessage">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span id="errorText">访问秘钥错误，请重试</span>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                如果您没有访问秘钥，请联系分享者获取
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('keyForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const accessKey = document.getElementById('accessKey').value.trim();
            const errorMessage = document.getElementById('errorMessage');
            const submitBtn = this.querySelector('button[type="submit"]');
            
            if (!accessKey) {
                showError('请输入访问秘钥');
                return;
            }
            
            // 显示加载状态
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>验证中...';
            errorMessage.style.display = 'none';
            
            try {
                const response = await fetch('/api/verify_share_key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        share_id: '{{ share_id }}',
                        access_key: accessKey
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 验证成功，跳转到分享页面
                    window.location.href = data.redirect_url;
                } else {
                    showError(data.message || '访问秘钥错误，请重试');
                }
            } catch (error) {
                console.error('验证失败:', error);
                showError('网络错误，请稍后重试');
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });
        
        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorMessage.style.display = 'block';
        }
        
        // 自动聚焦到输入框
        document.getElementById('accessKey').focus();
        
        // 输入框自动转换为大写
        document.getElementById('accessKey').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
